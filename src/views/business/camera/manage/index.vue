<script setup lang="tsx">
import { NDivider, NTag } from 'naive-ui';
import { fetchBatchDeleteCamera, fetchGetCameraList } from '@/service/api/business/camera';
import { useAppStore } from '@/store/modules/app';
import { useAuth } from '@/hooks/business/auth';
import { useDownload } from '@/hooks/business/download';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import ButtonIcon from '@/components/custom/button-icon.vue';
import CameraOperateDrawer from './modules/camera-operate-drawer.vue';
import CameraSearch from './modules/camera-search.vue';

defineOptions({
  name: 'CameraList'
});

const appStore = useAppStore();
const { download } = useDownload();
const { hasAuth } = useAuth();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetCameraList,
  apiParams: {
    pageNum: 1,
    pageSize: 10,
    // if you want to use the searchParams in Form, you need to define the following properties, and the value is null
    // the value can not be undefined, otherwise the property in Form will not be reactive
    cameraNumber: null,
    cameraName: null,
    buildingNumber: null,
    floorNumber: null,
    deviceModel: null,
    installDate: null,
    alertCount: null,
    soundColumnId: null,
    signalStrength: null,
    cameraLocation: null,
    ipAddress: null,
    loginUsername: null,
    loginPassword: null,
    cameraStatus: null,
    lastOnlineTime: null,
    resolution: null,
    nightVision: null,
    viewingAngle: null,
    aiFunctions: null,
    storageMethod: null,
    dataEncryption: null,
    streamPullUrl: null,
    params: {}
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      ellipsis: true
    },
    {
      key: 'cameraNumber',
      title: '摄像头编号',
      align: 'center',
      ellipsis: true
    },
    {
      key: 'cameraName',
      title: '摄像头名称',
      align: 'center',
      ellipsis: true
    },
    {
      key: 'buildingNumber',
      title: '楼栋号',
      align: 'center',
      ellipsis: true
    },
    {
      key: 'floorNumber',
      title: '楼层号',
      align: 'center',
      ellipsis: true
    },
    {
      key: 'cameraLocation',
      title: '安装位置',
      align: 'center',
      ellipsis: true
    },
    {
      key: 'deviceModel',
      title: '设备型号',
      align: 'center',
      ellipsis: true
    },
    {
      key: 'cameraStatus',
      title: '设备状态',
      align: 'center',
      render: row => {
        const statusMap: Record<string, { label: string; type: 'success' | 'error' | 'warning' | 'default' }> = {
          online: { label: '在线', type: 'success' },
          offline: { label: '离线', type: 'error' },
          maintenance: { label: '维护中', type: 'warning' }
        };
        const status = statusMap[row.cameraStatus] || { label: row.cameraStatus || '未知', type: 'default' };
        return <NTag type={status.type}>{status.label}</NTag>;
      }
    },
    {
      key: 'signalStrength',
      title: '信号强度',
      align: 'center',
      ellipsis: true
    },
    {
      key: 'installDate',
      title: '安装日期',
      align: 'center',
      width: 200
    },
    {
      key: 'alertCount',
      title: '报警次数',
      align: 'center',
      ellipsis: true
    },
    {
      key: 'lastOnlineTime',
      title: '最后在线时间',
      align: 'center',
      width: 200
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 200,
      fixed: 'right',
      render: row => {
        const showEdit = hasAuth('business:camera:edit');
        const showDelete = hasAuth('business:camera:remove');
        const showDivider = showEdit && showDelete;

        return (
          <div class="flex-center gap-8px">
            {showEdit && (
              <ButtonIcon
                text
                type="primary"
                icon="material-symbols:drive-file-rename-outline-outline"
                tooltipContent={$t('common.edit')}
                onClick={() => edit(row.id!)}
              />
            )}
            {showDivider && <NDivider vertical />}
            {showDelete && (
              <ButtonIcon
                text
                type="error"
                icon="material-symbols:delete-outline"
                tooltipContent={$t('common.delete')}
                popconfirmContent={$t('common.confirmDelete')}
                onPositiveClick={() => handleDelete(row.id!)}
              />
            )}
          </div>
        );
      }
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  // request
  const { error } = await fetchBatchDeleteCamera(checkedRowKeys.value);
  if (error) return;
  onBatchDeleted();
}

async function handleDelete(id: CommonType.IdType) {
  // request
  const { error } = await fetchBatchDeleteCamera([id]);
  if (error) return;
  onDeleted();
}

function edit(id: CommonType.IdType) {
  handleEdit('id', id);
}

function handleExport() {
  download('/business/cameraInfo/export', searchParams, `摄像头管理_${new Date().getTime()}.xlsx`);
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <CameraSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard title="摄像头管理" :bordered="false" size="small" class="card-wrapper sm:flex-1-hidden">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          :show-add="hasAuth('business:camera:add')"
          :show-delete="hasAuth('business:camera:remove')"
          :show-export="hasAuth('business:camera:export')"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @export="handleExport"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="1600"
        :flex-height="!appStore.isMobile"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <CameraOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
