<script setup lang="ts">
import { ref } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'PaymentSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();


const model = defineModel<Api.Business.PaymentSearchParams>('model', { required: true });


async function reset() {
  Object.assign(model.value.params!, {});
  await restoreValidation();
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem :title="$t('common.search')" name="user-search">
        <NForm ref="formRef" :model="model" label-placement="left" :label-width="80">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="提醒编号（如REM20240415001）" path="reminderNo" class="pr-24px">
              <NInput v-model:value="model.reminderNo" placeholder="请输入提醒编号（如REM20240415001）" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="关联租赁合同表的合同 ID" path="contractId" class="pr-24px">
              <NInput v-model:value="model.contractId" placeholder="请输入关联租赁合同表的合同 ID" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="提醒类型（缴费提醒、续签提醒）" path="reminderType" class="pr-24px">
              <NSelect
                v-model:value="model.reminderType"
                placeholder="请选择提醒类型（缴费提醒、续签提醒）"
                :options="[]"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="计划提醒时间（精确到分秒）" path="reminderTime" class="pr-24px">
              <NDatePicker
                v-model:formatted-value="model.reminderTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="提前提醒天数" path="advanceDays" class="pr-24px">
              <NInput v-model:value="model.advanceDays" placeholder="请输入提前提醒天数" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="发送方式（短信、微信、邮件等，多方式用逗号分隔）" path="sendMethod" class="pr-24px">
              <NInput v-model:value="model.sendMethod" placeholder="请输入发送方式（短信、微信、邮件等，多方式用逗号分隔）" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="提醒内容模板" path="messageContent" class="pr-24px">
              <NInput v-model:value="model.messageContent" placeholder="请输入提醒内容模板" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="提醒状态（未发送、已发送、发送失败、已取消）" path="status" class="pr-24px">
              <NSelect
                v-model:value="model.status"
                placeholder="请选择提醒状态（未发送、已发送、发送失败、已取消）"
                :options="[]"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="实际发送时间（精确到分秒）" path="sendTime" class="pr-24px">
              <NDatePicker
                v-model:formatted-value="model.sendTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="发送结果描述（如失败原因）" path="sendResult" class="pr-24px">
              <NInput v-model:value="model.sendResult" placeholder="请输入发送结果描述（如失败原因）" />
            </NFormItemGi>
            <NFormItemGi span="24" class="pr-24px">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
