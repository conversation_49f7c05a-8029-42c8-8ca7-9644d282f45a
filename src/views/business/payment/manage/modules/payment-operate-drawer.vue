<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { fetchCreatePayment, fetchUpdatePayment } from '@/service/api/business/payment';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'PaymentOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Business.Payment | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});


const { formRef, validate, restoreValidation } = useNaiveForm();
const { createRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增缴费计划管理',
    edit: '编辑缴费计划管理'
  };
  return titles[props.operateType];
});

type Model = Api.Business.PaymentOperateParams;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
      reminderNo: '',
      contractId: undefined,
      reminderType: '',
      reminderTime: undefined,
      advanceDays: undefined,
      sendMethod: '',
      messageContent: '',
      status: '',
      sendTime: undefined,
      sendResult: '',
      remark: '',
  };
}

type RuleKey = Extract<
  keyof Model,
  | 'reminderNo'
  | 'contractId'
  | 'reminderType'
  | 'reminderTime'
  | 'advanceDays'
  | 'sendMethod'
  | 'messageContent'
  | 'status'
  | 'sendTime'
  | 'sendResult'
  | 'remark'
  | 'createTime'
  | 'createBy'
  | 'updateTime'
  | 'updateBy'
  | 'version'
>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  reminderNo: createRequiredRule('提醒编号（如REM20240415001）不能为空'),
  contractId: createRequiredRule('关联租赁合同表的合同 ID不能为空'),
  reminderType: createRequiredRule('提醒类型（缴费提醒、续签提醒）不能为空'),
  reminderTime: createRequiredRule('计划提醒时间（精确到分秒）不能为空'),
  advanceDays: createRequiredRule('提前提醒天数不能为空'),
  sendMethod: createRequiredRule('发送方式（短信、微信、邮件等，多方式用逗号分隔）不能为空'),
  messageContent: createRequiredRule('提醒内容模板不能为空'),
  status: createRequiredRule('提醒状态（未发送、已发送、发送失败、已取消）不能为空'),
  sendTime: createRequiredRule('实际发送时间（精确到分秒）不能为空'),
  sendResult: createRequiredRule('发送结果描述（如失败原因）不能为空'),
  remark: createRequiredRule('关于提醒的备注信息不能为空'),
  createTime: createRequiredRule('记录创建时间（精确到分秒）不能为空'),
  createBy: createRequiredRule('创建该记录的人员不能为空'),
  updateTime: createRequiredRule('记录更新时间（精确到分秒）不能为空'),
  updateBy: createRequiredRule('更新该记录的人员不能为空'),
  version: createRequiredRule('版本号，用于乐观锁控制不能为空')
};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  const { id, reminderNo, contractId, reminderType, reminderTime, advanceDays, sendMethod, messageContent, status, sendTime, sendResult, remark } = model;

  // request
  if (props.operateType === 'add') {
    const { error } = await fetchCreatePayment({ reminderNo, contractId, reminderType, reminderTime, advanceDays, sendMethod, messageContent, status, sendTime, sendResult, remark });
    if (error) return;
  }

  if (props.operateType === 'edit') {
    const { error } = await fetchUpdatePayment({ id, reminderNo, contractId, reminderType, reminderTime, advanceDays, sendMethod, messageContent, status, sendTime, sendResult, remark });
    if (error) return;
  }

  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="800" class="max-w-90%">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem label="提醒编号（如REM20240415001）" path="reminderNo">
          <NInput v-model:value="model.reminderNo" placeholder="请输入提醒编号（如REM20240415001）" />
        </NFormItem>
        <NFormItem label="关联租赁合同表的合同 ID" path="contractId">
          <NInput v-model:value="model.contractId" placeholder="请输入关联租赁合同表的合同 ID" />
        </NFormItem>
        <NFormItem label="提醒类型（缴费提醒、续签提醒）" path="reminderType">
          <NInput v-model:value="model.reminderType" placeholder="请输入提醒类型（缴费提醒、续签提醒）" />
        </NFormItem>
        <NFormItem label="计划提醒时间（精确到分秒）" path="reminderTime">
          <NDatePicker
            v-model:formatted-value="model.reminderTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            clearable
          />
        </NFormItem>
        <NFormItem label="提前提醒天数" path="advanceDays">
          <NInput v-model:value="model.advanceDays" placeholder="请输入提前提醒天数" />
        </NFormItem>
        <NFormItem label="发送方式（短信、微信、邮件等，多方式用逗号分隔）" path="sendMethod">
          <NInput v-model:value="model.sendMethod" placeholder="请输入发送方式（短信、微信、邮件等，多方式用逗号分隔）" />
        </NFormItem>
        <NFormItem label="提醒内容模板" path="messageContent">
          <NInput
            v-model:value="model.messageContent"
            :rows="3"
            type="textarea"
            placeholder="请输入提醒内容模板"
          />
        </NFormItem>
        <NFormItem label="提醒状态（未发送、已发送、发送失败、已取消）" path="status">
          <NInput v-model:value="model.status" placeholder="请输入提醒状态（未发送、已发送、发送失败、已取消）" />
        </NFormItem>
        <NFormItem label="实际发送时间（精确到分秒）" path="sendTime">
          <NDatePicker
            v-model:formatted-value="model.sendTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            clearable
          />
        </NFormItem>
        <NFormItem label="发送结果描述（如失败原因）" path="sendResult">
          <NInput v-model:value="model.sendResult" placeholder="请输入发送结果描述（如失败原因）" />
        </NFormItem>
        <NFormItem label="关于提醒的备注信息" path="remark">
          <NInput
            v-model:value="model.remark"
            :rows="3"
            type="textarea"
            placeholder="请输入关于提醒的备注信息"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
