<script setup lang="tsx">
import { NDivider } from 'naive-ui';
import { fetchBatchDeletePayment, fetchGetPaymentList } from '@/service/api/business/payment';
import { useAppStore } from '@/store/modules/app';
import { useAuth } from '@/hooks/business/auth';
import { useDownload } from '@/hooks/business/download';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import ButtonIcon from '@/components/custom/button-icon.vue';
import PaymentOperateDrawer from './modules/payment-operate-drawer.vue';
import PaymentSearch from './modules/payment-search.vue';

defineOptions({
  name: 'PaymentList'
});

const appStore = useAppStore();
const { download } = useDownload();
const { hasAuth } = useAuth();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetPaymentList,
  apiParams: {
    pageNum: 1,
    pageSize: 10,
    // if you want to use the searchParams in Form, you need to define the following properties, and the value is null
    // the value can not be undefined, otherwise the property in Form will not be reactive
    reminderNo: null,
    contractId: null,
    reminderType: null,
    reminderTime: null,
    advanceDays: null,
    sendMethod: null,
    messageContent: null,
    status: null,
    sendTime: null,
    sendResult: null,
    params: {}
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'id',
      title: '主键，唯一标识每条提醒记录',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'reminderNo',
      title: '提醒编号（如REM20240415001）',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'contractId',
      title: '关联租赁合同表的合同 ID',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'reminderType',
      title: '提醒类型（缴费提醒、续签提醒）',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'reminderTime',
      title: '计划提醒时间（精确到分秒）',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'advanceDays',
      title: '提前提醒天数',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'sendMethod',
      title: '发送方式（短信、微信、邮件等，多方式用逗号分隔）',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'messageContent',
      title: '提醒内容模板',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'status',
      title: '提醒状态（未发送、已发送、发送失败、已取消）',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'sendTime',
      title: '实际发送时间（精确到分秒）',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'sendResult',
      title: '发送结果描述（如失败原因）',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'remark',
      title: '关于提醒的备注信息',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 130,
      render: row => {
        const divider = () => {
          if (!hasAuth('business:payment:edit') || !hasAuth('business:payment:remove')) {
            return null;
          }
          return <NDivider vertical />;
        };

        const editBtn = () => {
          if (!hasAuth('business:payment:edit')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="primary"
              icon="material-symbols:drive-file-rename-outline-outline"
              tooltipContent={$t('common.edit')}
              onClick={() => edit(row.id!)}
            />
          );
        };

        const deleteBtn = () => {
          if (!hasAuth('business:payment:remove')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="error"
              icon="material-symbols:delete-outline"
              tooltipContent={$t('common.delete')}
              popconfirmContent={$t('common.confirmDelete')}
              onPositiveClick={() => handleDelete(row.id!)}
            />
          );
        };

        return (
          <div class="flex-center gap-8px">
            {editBtn()}
            {divider()}
            {deleteBtn()}
          </div>
        );
      }
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  // request
  const { error } = await fetchBatchDeletePayment(checkedRowKeys.value);
  if (error) return;
  onBatchDeleted();
}

async function handleDelete(id: CommonType.IdType) {
  // request
  const { error } = await fetchBatchDeletePayment([id]);
  if (error) return;
  onDeleted();
}

function edit(id: CommonType.IdType) {
  handleEdit('id', id);
}

function handleExport() {
  download('/business/paymentReminder/export', searchParams, `缴费计划管理_${new Date().getTime()}.xlsx`);
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <PaymentSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard title="缴费计划管理列表" :bordered="false" size="small" class="card-wrapper sm:flex-1-hidden">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          :show-add="hasAuth('business:payment:add')"
          :show-delete="hasAuth('business:payment:remove')"
          :show-export="hasAuth('business:payment:export')"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @export="handleExport"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <PaymentOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
