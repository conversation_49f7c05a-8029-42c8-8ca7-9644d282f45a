<script setup lang="ts">
import { ref } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'TenantSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();


const model = defineModel<Api.Business.TenantSearchParams>('model', { required: true });

// 租户状态选项
const statusOptions = [
  { label: '在住', value: '在住' },
  { label: '已退租', value: '已退租' },
  { label: '预定', value: '预定' },
  { label: '黑名单', value: '黑名单' }
];


async function reset() {
  Object.assign(model.value.params!, {});
  await restoreValidation();
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem :title="$t('common.search')" name="user-search">
        <NForm ref="formRef" :model="model" label-placement="left" :label-width="100">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="租户编号" path="tenantNo" class="pr-24px">
              <NInput v-model:value="model.tenantNo" placeholder="请输入租户编号" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="姓名" path="name" class="pr-24px">
              <NInput v-model:value="model.name" placeholder="请输入姓名" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="联系方式" path="contactNumber" class="pr-24px">
              <NInput v-model:value="model.contactNumber" placeholder="请输入联系方式" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="租户状态" path="status" class="pr-24px">
              <NSelect
                v-model:value="model.status"
                placeholder="请选择租户状态"
                :options="statusOptions"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="入住时间" path="checkInTime" class="pr-24px">
              <NDatePicker
                v-model:formatted-value="model.checkInTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择入住时间"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="退租时间" path="checkOutTime" class="pr-24px">
              <NDatePicker
                v-model:formatted-value="model.checkOutTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择退租时间"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24" class="pr-24px">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
