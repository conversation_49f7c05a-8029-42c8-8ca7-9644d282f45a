<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { NDivider, NGrid, NFormItemGi, NSelect, NInputNumber } from 'naive-ui';
import { fetchCreateTenant, fetchUpdateTenant } from '@/service/api/business/tenant';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'TenantOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Business.Tenant | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});


const { formRef, validate, restoreValidation } = useNaiveForm();
const { createRequiredRule } = useFormRules();

// 下拉选项定义
const genderOptions = [
  { label: '男', value: '男' },
  { label: '女', value: '女' }
];

const leaseTermTypeOptions = [
  { label: '长期', value: '长期' },
  { label: '短期', value: '短期' }
];

const statusOptions = [
  { label: '在住', value: '在住' },
  { label: '已退租', value: '已退租' },
  { label: '预定', value: '预定' },
  { label: '黑名单', value: '黑名单' }
];

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增租户',
    edit: '编辑租户'
  };
  return titles[props.operateType];
});

type Model = Api.Business.TenantOperateParams;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
      tenantNo: '',
      name: '',
      gender: '',
      idCardNumber: '',
      idCardFrontUrl: '',
      idCardBackUrl: '',
      dateOfBirth: undefined,
      nation: '',
      contactNumber: '',
      emergencyContact: '',
      emergencyContactNumber: '',
      companyInfo: '',
      householdRegister: '',
      currentAddress: '',
      checkInTime: undefined,
      checkOutTime: undefined,
      leaseTermType: '',
      expectedLeaseTerm: undefined,
      status: '',
      avatarUrl: '',
      remark: '',
  };
}

type RuleKey = Extract<
  keyof Model,
  | 'name'
  | 'gender'
  | 'contactNumber'
  | 'idCardNumber'
>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: createRequiredRule('姓名不能为空'),
  gender: createRequiredRule('性别不能为空'),
  contactNumber: createRequiredRule('联系方式不能为空'),
  idCardNumber: createRequiredRule('身份证号码不能为空')
};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  const { id, tenantNo, name, gender, idCardNumber, idCardFrontUrl, idCardBackUrl, dateOfBirth, nation, contactNumber, emergencyContact, emergencyContactNumber, companyInfo, householdRegister, currentAddress, checkInTime, checkOutTime, leaseTermType, expectedLeaseTerm, status, avatarUrl, remark } = model;

  // request
  if (props.operateType === 'add') {
    const { error } = await fetchCreateTenant({ tenantNo, name, gender, idCardNumber, idCardFrontUrl, idCardBackUrl, dateOfBirth, nation, contactNumber, emergencyContact, emergencyContactNumber, companyInfo, householdRegister, currentAddress, checkInTime, checkOutTime, leaseTermType, expectedLeaseTerm, status, avatarUrl, remark });
    if (error) return;
  }

  if (props.operateType === 'edit') {
    const { error } = await fetchUpdateTenant({ id, tenantNo, name, gender, idCardNumber, idCardFrontUrl, idCardBackUrl, dateOfBirth, nation, contactNumber, emergencyContact, emergencyContactNumber, companyInfo, householdRegister, currentAddress, checkInTime, checkOutTime, leaseTermType, expectedLeaseTerm, status, avatarUrl, remark });
    if (error) return;
  }

  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="800" class="max-w-90%">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="120">
        <!-- 基本信息 -->
        <NDivider title-placement="left">基本信息</NDivider>
        <NGrid :cols="2" :x-gap="16">
          <NFormItemGi label="租户编号" path="tenantNo">
            <NInput v-model:value="model.tenantNo" placeholder="系统自动生成" readonly />
          </NFormItemGi>
          <NFormItemGi label="姓名" path="name">
            <NInput v-model:value="model.name" placeholder="请输入姓名" />
          </NFormItemGi>
          <NFormItemGi label="性别" path="gender">
            <NSelect
              v-model:value="model.gender"
              placeholder="请选择性别"
              :options="genderOptions"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="身份证号码" path="idCardNumber">
            <NInput v-model:value="model.idCardNumber" placeholder="请输入身份证号码" />
          </NFormItemGi>
          <NFormItemGi label="出生日期" path="dateOfBirth">
            <NDatePicker
              v-model:formatted-value="model.dateOfBirth"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择出生日期"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="民族" path="nation">
            <NInput v-model:value="model.nation" placeholder="请输入民族" />
          </NFormItemGi>
        </NGrid>

        <!-- 联系信息 -->
        <NDivider title-placement="left">联系信息</NDivider>
        <NGrid :cols="2" :x-gap="16">
          <NFormItemGi label="联系方式" path="contactNumber">
            <NInput v-model:value="model.contactNumber" placeholder="请输入联系方式" />
          </NFormItemGi>
          <NFormItemGi label="紧急联系人" path="emergencyContact">
            <NInput v-model:value="model.emergencyContact" placeholder="请输入紧急联系人姓名" />
          </NFormItemGi>
          <NFormItemGi label="紧急联系人电话" path="emergencyContactNumber">
            <NInput v-model:value="model.emergencyContactNumber" placeholder="请输入紧急联系人电话" />
          </NFormItemGi>
          <NFormItemGi label="公司信息" path="companyInfo">
            <NInput v-model:value="model.companyInfo" placeholder="请输入公司信息" />
          </NFormItemGi>
        </NGrid>

        <!-- 地址信息 -->
        <NDivider title-placement="left">地址信息</NDivider>
        <NGrid :cols="1" :x-gap="16">
          <NFormItemGi label="户籍地址" path="householdRegister">
            <NInput v-model:value="model.householdRegister" placeholder="请输入户籍地址" />
          </NFormItemGi>
          <NFormItemGi label="现住址" path="currentAddress">
            <NInput v-model:value="model.currentAddress" placeholder="请输入现住址" />
          </NFormItemGi>
        </NGrid>

        <!-- 租赁信息 -->
        <NDivider title-placement="left">租赁信息</NDivider>
        <NGrid :cols="2" :x-gap="16">
          <NFormItemGi label="入住时间" path="checkInTime">
            <NDatePicker
              v-model:formatted-value="model.checkInTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择入住时间"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="退租时间" path="checkOutTime">
            <NDatePicker
              v-model:formatted-value="model.checkOutTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择退租时间"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="租约类型" path="leaseTermType">
            <NSelect
              v-model:value="model.leaseTermType"
              placeholder="请选择租约类型"
              :options="leaseTermTypeOptions"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="预计租期(月)" path="expectedLeaseTerm">
            <NInputNumber
              v-model:value="model.expectedLeaseTerm"
              placeholder="请输入预计租期"
              :min="1"
              :max="120"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="租户状态" path="status">
            <NSelect
              v-model:value="model.status"
              placeholder="请选择租户状态"
              :options="statusOptions"
              clearable
            />
          </NFormItemGi>
        </NGrid>

        <!-- 附件信息 -->
        <NDivider title-placement="left">附件信息</NDivider>
        <NGrid :cols="2" :x-gap="16">
          <NFormItemGi label="身份证正面" path="idCardFrontUrl">
            <NInput v-model:value="model.idCardFrontUrl" placeholder="请输入身份证正面照片URL" />
          </NFormItemGi>
          <NFormItemGi label="身份证反面" path="idCardBackUrl">
            <NInput v-model:value="model.idCardBackUrl" placeholder="请输入身份证反面照片URL" />
          </NFormItemGi>
          <NFormItemGi label="头像" path="avatarUrl">
            <NInput v-model:value="model.avatarUrl" placeholder="请输入头像URL" />
          </NFormItemGi>
        </NGrid>

        <!-- 其他信息 -->
        <NDivider title-placement="left">其他信息</NDivider>
        <NFormItem label="备注" path="remark">
          <NInput
            v-model:value="model.remark"
            :rows="3"
            type="textarea"
            placeholder="请输入备注信息"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
