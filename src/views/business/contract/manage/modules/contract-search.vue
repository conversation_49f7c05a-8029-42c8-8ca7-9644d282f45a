<script setup lang="ts">
import { ref } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'ContractSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();


const model = defineModel<Api.Business.ContractSearchParams>('model', { required: true });

const statusOptions = [
  { label: '生效中', value: 'active' },
  { label: '待签署', value: 'pending' },
  { label: '已终止', value: 'terminated' },
  { label: '已过期', value: 'expired' }
];

async function reset() {
  Object.assign(model.value.params!, {});
  await restoreValidation();
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem :title="$t('common.search')" name="user-search">
        <NForm ref="formRef" :model="model" label-placement="left" :label-width="80">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="合同编号" path="contractNo" class="pr-24px">
              <NInput v-model:value="model.contractNo" placeholder="请输入合同编号" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="公寓房间" path="apartmentId" class="pr-24px">
              <NInput v-model:value="model.apartmentId" placeholder="请输入公寓房间" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="租客姓名" path="partyBName" class="pr-24px">
              <NInput v-model:value="model.partyBName" placeholder="请输入租客姓名" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="出租方" path="partyAName" class="pr-24px">
              <NInput v-model:value="model.partyAName" placeholder="请输入出租方" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="合同状态" path="status" class="pr-24px">
              <NSelect
                v-model:value="model.status"
                placeholder="请选择合同状态"
                :options="statusOptions"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="租期开始" path="startLeaseStartTime" class="pr-24px">
              <NDatePicker
                v-model:formatted-value="model.startLeaseStartTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="租期开始时间范围-开始"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="至" path="endLeaseStartTime" class="pr-24px">
              <NDatePicker
                v-model:formatted-value="model.endLeaseStartTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="租期开始时间范围-结束"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="签署时间" path="startSignTime" class="pr-24px">
              <NDatePicker
                v-model:formatted-value="model.startSignTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="签署时间范围-开始"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="至" path="endSignTime" class="pr-24px">
              <NDatePicker
                v-model:formatted-value="model.endSignTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="签署时间范围-结束"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24" class="pr-24px">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
