<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { NDivider, NFormItemGi, NGrid, NInputNumber, NSwitch } from 'naive-ui';
import { fetchCreateContract, fetchUpdateContract } from '@/service/api/business/contract';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'ContractOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Business.Contract | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { createRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增合同',
    edit: '编辑合同'
  };
  return titles[props.operateType];
});

type Model = Api.Business.ContractOperateParams;

const model: Model = reactive(createDefaultModel());

// 选项数据
const statusOptions = [
  { label: '生效中', value: 'active' },
  { label: '待签署', value: 'pending' },
  { label: '已终止', value: 'terminated' },
  { label: '已过期', value: 'expired' }
];

const depositTypeOptions = [
  { label: '1个月租金', value: '1month' },
  { label: '2个月租金', value: '2month' },
  { label: '3个月租金', value: '3month' },
  { label: '固定金额', value: 'fixed' }
];

const paymentCycleOptions = [
  { label: '月付', value: 'monthly' },
  { label: '季付', value: 'quarterly' },
  { label: '半年付', value: 'semiannual' },
  { label: '年付', value: 'annual' }
];

function createDefaultModel(): Model {
  return {
    contractNo: undefined,
    apartmentId: undefined,
    partyAName: undefined,
    partyACreditCode: undefined,
    partyARepresentative: undefined,
    partyAContact: undefined,
    partyAAddress: undefined,
    partyBName: undefined,
    partyBIdCard: undefined,
    partyBContact: undefined,
    partyBEmergencyContact: undefined,
    partyBEmergencyPhone: undefined,
    leaseStartTime: undefined,
    leaseEndTime: undefined,
    leaseTermDays: undefined,
    maxOccupancy: undefined,
    monthlyRent: undefined,
    depositAmount: undefined,
    depositType: undefined,
    paymentCycle: undefined,
    paymentDate: undefined,
    propertyFee: undefined,
    feeIncludeWaterElectric: 0,
    status: undefined,
    signTime: undefined,
    contractFileUrl: undefined,
    signFileUrl: undefined,
    remark: undefined,
    terminationTime: undefined,
    terminationReason: undefined
  };
}

type RuleKey = Extract<
  keyof Model,
  | 'contractNo'
  | 'tenantId'
  | 'apartmentId'
  | 'partyAName'
  | 'partyACreditCode'
  | 'partyARepresentative'
  | 'partyAContact'
  | 'partyAAddress'
  | 'partyBName'
  | 'partyBIdCard'
  | 'partyBContact'
  | 'partyBEmergencyContact'
  | 'partyBEmergencyPhone'
  | 'leaseStartTime'
  | 'leaseEndTime'
  | 'leaseTermDays'
  | 'maxOccupancy'
  | 'monthlyRent'
  | 'depositAmount'
  | 'depositType'
  | 'paymentCycle'
  | 'paymentDate'
  | 'propertyFee'
  | 'feeIncludeWaterElectric'
  | 'status'
  | 'signTime'
  | 'contractFileUrl'
  | 'signFileUrl'
  | 'remark'
  | 'createTime'
  | 'createBy'
  | 'updateTime'
  | 'updateBy'
  | 'version'
  | 'terminationTime'
  | 'terminationReason'
>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  contractNo: createRequiredRule('合同编号不能为空')
  // tenantId: createRequiredRule('租户ID不能为空')
  // apartmentId: createRequiredRule('公寓房间不能为空'),
  // partyAName: createRequiredRule('出租方不能为空'),
  // partyACreditCode: createRequiredRule('信用代码不能为空'),
  // partyARepresentative: createRequiredRule('法人代表不能为空'),
  // partyAContact: createRequiredRule('出租方电话不能为空'),
  // partyAAddress: createRequiredRule('出租方地址不能为空'),
  // partyBName: createRequiredRule('租客姓名不能为空'),
  // partyBIdCard: createRequiredRule('身份证号不能为空'),
  // partyBContact: createRequiredRule('租客电话不能为空'),
  // partyBEmergencyContact: createRequiredRule('紧急联系人不能为空'),
  // partyBEmergencyPhone: createRequiredRule('紧急联系电话不能为空'),
  // leaseStartTime: createRequiredRule('租期开始不能为空'),
  // leaseEndTime: createRequiredRule('租期结束不能为空'),
  // leaseTermDays: createRequiredRule('租期天数不能为空'),
  // maxOccupancy: createRequiredRule('入住人数不能为空'),
  // monthlyRent: createRequiredRule('月租金不能为空'),
  // depositAmount: createRequiredRule('押金金额不能为空'),
  // depositType: createRequiredRule('押金类型不能为空'),
  // paymentCycle: createRequiredRule('支付周期不能为空'),
  // paymentDate: createRequiredRule('付款日不能为空'),
  // propertyFee: createRequiredRule('物业费不能为空'),
  // feeIncludeWaterElectric: createRequiredRule('包含水电费不能为空'),
  // status: createRequiredRule('合同状态不能为空'),
  // signTime: createRequiredRule('签署时间不能为空'),
  // contractFileUrl: createRequiredRule('合同文件不能为空'),
  // signFileUrl: createRequiredRule('签字页不能为空')
  // remark: createRequiredRule('备注信息不能为空'),
  // createTime: createRequiredRule('创建时间不能为空'),
  // createBy: createRequiredRule('创建人不能为空'),
  // updateTime: createRequiredRule('更新时间不能为空'),
  // updateBy: createRequiredRule('更新人不能为空'),
  // version: createRequiredRule('版本号不能为空'),
  // terminationTime: createRequiredRule('终止时间不能为空'),
  // terminationReason: createRequiredRule('终止原因不能为空')
};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  const {
    id,
    contractNo,
    apartmentId,
    partyAName,
    partyACreditCode,
    partyARepresentative,
    partyAContact,
    partyAAddress,
    partyBName,
    partyBIdCard,
    partyBContact,
    partyBEmergencyContact,
    partyBEmergencyPhone,
    leaseStartTime,
    leaseEndTime,
    leaseTermDays,
    maxOccupancy,
    monthlyRent,
    depositAmount,
    depositType,
    paymentCycle,
    paymentDate,
    propertyFee,
    feeIncludeWaterElectric,
    status,
    signTime,
    contractFileUrl,
    signFileUrl,
    remark,
    terminationTime,
    terminationReason
  } = model;

  // request
  if (props.operateType === 'add') {
    const { error } = await fetchCreateContract({
      contractNo,
      apartmentId,
      partyAName,
      partyACreditCode,
      partyARepresentative,
      partyAContact,
      partyAAddress,
      partyBName,
      partyBIdCard,
      partyBContact,
      partyBEmergencyContact,
      partyBEmergencyPhone,
      leaseStartTime,
      leaseEndTime,
      leaseTermDays,
      maxOccupancy,
      monthlyRent,
      depositAmount,
      depositType,
      paymentCycle,
      paymentDate,
      propertyFee,
      feeIncludeWaterElectric,
      status,
      signTime,
      contractFileUrl,
      signFileUrl,
      remark,
      terminationTime,
      terminationReason
    });
    if (error) return;
  }

  if (props.operateType === 'edit') {
    const { error } = await fetchUpdateContract({
      id,
      contractNo,
      apartmentId,
      partyAName,
      partyACreditCode,
      partyARepresentative,
      partyAContact,
      partyAAddress,
      partyBName,
      partyBIdCard,
      partyBContact,
      partyBEmergencyContact,
      partyBEmergencyPhone,
      leaseStartTime,
      leaseEndTime,
      leaseTermDays,
      maxOccupancy,
      monthlyRent,
      depositAmount,
      depositType,
      paymentCycle,
      paymentDate,
      propertyFee,
      feeIncludeWaterElectric,
      status,
      signTime,
      contractFileUrl,
      signFileUrl,
      remark,
      terminationTime,
      terminationReason
    });
    if (error) return;
  }

  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="800" class="max-w-90%">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="120">
        <!-- 基本信息 -->
        <NDivider title-placement="left">基本信息</NDivider>
        <NGrid :cols="2" :x-gap="16">
          <NFormItemGi label="合同编号" path="contractNo">
            <NInput v-model:value="model.contractNo" placeholder="请输入合同编号" />
          </NFormItemGi>
          <NFormItemGi label="公寓房间" path="apartmentId">
            <NInput v-model:value="model.apartmentId" placeholder="请输入公寓房间" />
          </NFormItemGi>
          <NFormItemGi label="合同状态" path="status">
            <NSelect v-model:value="model.status" placeholder="请选择合同状态" :options="statusOptions" clearable />
          </NFormItemGi>
        </NGrid>

        <!-- 出租方信息 -->
        <NDivider title-placement="left">出租方信息</NDivider>
        <NGrid :cols="2" :x-gap="16">
          <NFormItemGi label="出租方" path="partyAName">
            <NInput v-model:value="model.partyAName" placeholder="请输入出租方" />
          </NFormItemGi>
          <NFormItemGi label="信用代码" path="partyACreditCode">
            <NInput v-model:value="model.partyACreditCode" placeholder="请输入统一社会信用代码" />
          </NFormItemGi>
          <NFormItemGi label="法人代表" path="partyARepresentative">
            <NInput v-model:value="model.partyARepresentative" placeholder="请输入法人代表" />
          </NFormItemGi>
          <NFormItemGi label="出租方电话" path="partyAContact">
            <NInput v-model:value="model.partyAContact" placeholder="请输入出租方电话" />
          </NFormItemGi>
        </NGrid>
        <NFormItem label="出租方地址" path="partyAAddress">
          <NInput v-model:value="model.partyAAddress" placeholder="请输入出租方地址" />
        </NFormItem>

        <!-- 租客信息 -->
        <NDivider title-placement="left">租客信息</NDivider>
        <NGrid :cols="2" :x-gap="16">
          <NFormItemGi label="租客姓名" path="partyBName">
            <NInput v-model:value="model.partyBName" placeholder="请输入租客姓名" />
          </NFormItemGi>
          <NFormItemGi label="身份证号" path="partyBIdCard">
            <NInput v-model:value="model.partyBIdCard" placeholder="请输入身份证号" />
          </NFormItemGi>
          <NFormItemGi label="租客电话" path="partyBContact">
            <NInput v-model:value="model.partyBContact" placeholder="请输入租客电话" />
          </NFormItemGi>
          <NFormItemGi label="紧急联系人" path="partyBEmergencyContact">
            <NInput v-model:value="model.partyBEmergencyContact" placeholder="请输入紧急联系人" />
          </NFormItemGi>
          <NFormItemGi label="紧急联系电话" path="partyBEmergencyPhone">
            <NInput v-model:value="model.partyBEmergencyPhone" placeholder="请输入紧急联系电话" />
          </NFormItemGi>
        </NGrid>

        <!-- 租赁条款 -->
        <NDivider title-placement="left">租赁条款</NDivider>
        <NGrid :cols="2" :x-gap="16">
          <NFormItemGi label="租期开始" path="leaseStartTime">
            <NDatePicker
              v-model:formatted-value="model.leaseStartTime"
              type="date"
              value-format="yyyy-MM-dd"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="租期结束" path="leaseEndTime">
            <NDatePicker v-model:formatted-value="model.leaseEndTime" type="date" value-format="yyyy-MM-dd" clearable />
          </NFormItemGi>
          <NFormItemGi label="入住人数" path="maxOccupancy">
            <NInputNumber v-model:value="model.maxOccupancy" placeholder="请输入入住人数" :min="1" />
          </NFormItemGi>
          <NFormItemGi label="月租金" path="monthlyRent">
            <NInputNumber v-model:value="model.monthlyRent" placeholder="请输入月租金" :min="0" :precision="2">
              <template #suffix>元</template>
            </NInputNumber>
          </NFormItemGi>
          <NFormItemGi label="押金金额" path="depositAmount">
            <NInputNumber v-model:value="model.depositAmount" placeholder="请输入押金金额" :min="0" :precision="2">
              <template #suffix>元</template>
            </NInputNumber>
          </NFormItemGi>
          <NFormItemGi label="押金类型" path="depositType">
            <NSelect
              v-model:value="model.depositType"
              placeholder="请选择押金类型"
              :options="depositTypeOptions"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="支付周期" path="paymentCycle">
            <NSelect
              v-model:value="model.paymentCycle"
              placeholder="请选择支付周期"
              :options="paymentCycleOptions"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="付款日" path="paymentDate">
            <NInputNumber v-model:value="model.paymentDate" placeholder="每月付款日" :min="1" :max="31" />
          </NFormItemGi>
          <NFormItemGi label="物业费" path="propertyFee">
            <NInputNumber v-model:value="model.propertyFee" placeholder="请输入物业费" :min="0" :precision="2">
              <template #suffix>元/月</template>
            </NInputNumber>
          </NFormItemGi>
          <NFormItemGi label="包含水电费" path="feeIncludeWaterElectric">
            <NSwitch v-model:value="model.feeIncludeWaterElectric" :default-value="0" :checked-value="1" />
          </NFormItemGi>
        </NGrid>

        <!-- 合同文件 -->
        <NDivider title-placement="left">合同文件</NDivider>
        <NGrid :cols="2" :x-gap="16">
          <NFormItemGi label="合同文件" path="contractFileUrl">
            <NInput v-model:value="model.contractFileUrl" placeholder="请输入合同文件URL" />
          </NFormItemGi>
          <NFormItemGi label="签字页" path="signFileUrl">
            <NInput v-model:value="model.signFileUrl" placeholder="请输入签字页URL" />
          </NFormItemGi>
          <NFormItemGi label="签署时间" path="signTime">
            <NDatePicker v-model:formatted-value="model.signTime" type="date" value-format="yyyy-MM-dd" clearable />
          </NFormItemGi>
        </NGrid>

        <!-- 其他信息 -->
        <NDivider title-placement="left">其他信息</NDivider>
        <NFormItem label="备注信息" path="remark">
          <NInput v-model:value="model.remark" :rows="3" type="textarea" placeholder="请输入备注信息" />
        </NFormItem>
        <NGrid :cols="2" :x-gap="16">
          <NFormItemGi label="终止时间" path="terminationTime">
            <NDatePicker
              v-model:formatted-value="model.terminationTime"
              type="date"
              value-format="yyyy-MM-dd"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="终止原因" path="terminationReason">
            <NInput v-model:value="model.terminationReason" placeholder="请输入终止原因" />
          </NFormItemGi>
        </NGrid>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
