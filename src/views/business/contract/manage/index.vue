<script setup lang="tsx">
import { NDivider, NTag } from 'naive-ui';
import { fetchBatchDeleteContract, fetchGetContractList } from '@/service/api/business/contract';
import { useAppStore } from '@/store/modules/app';
import { useAuth } from '@/hooks/business/auth';
import { useDownload } from '@/hooks/business/download';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import ButtonIcon from '@/components/custom/button-icon.vue';
import ContractOperateDrawer from './modules/contract-operate-drawer.vue';
import ContractSearch from './modules/contract-search.vue';

defineOptions({
  name: 'ContractList'
});

const appStore = useAppStore();
const { download } = useDownload();
const { hasAuth } = useAuth();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetContractList,
  apiParams: {
    pageNum: 1,
    pageSize: 10,
    // 搜索参数定义
    contractNo: null,
    apartmentId: null,
    partyAName: null,
    partyBName: null,
    status: null,
    startLeaseStartTime: null,
    endLeaseStartTime: null,
    startSignTime: null,
    endSignTime: null,
    params: {}
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'contractNo',
      title: '合同编号',
      align: 'center',
      minWidth: 140
    },
    {
      key: 'apartmentId',
      title: '公寓房间',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'partyBName',
      title: '租客姓名',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'leaseStartTime',
      title: '租期开始',
      align: 'center',
      minWidth: 120,
      render: row => {
        if (!row.leaseStartTime) return '-';
        return new Date(row.leaseStartTime).toLocaleDateString();
      }
    },
    {
      key: 'leaseEndTime',
      title: '租期结束',
      align: 'center',
      minWidth: 120,
      render: row => {
        if (!row.leaseEndTime) return '-';
        return new Date(row.leaseEndTime).toLocaleDateString();
      }
    },
    {
      key: 'monthlyRent',
      title: '月租金',
      align: 'center',
      minWidth: 100,
      render: row => {
        if (!row.monthlyRent) return '-';
        return `¥${row.monthlyRent}`;
      }
    },
    {
      key: 'status',
      title: '合同状态',
      align: 'center',
      minWidth: 100,
      render: row => {
        const statusMap: Record<string, { label: string; type: 'success' | 'warning' | 'error' | 'info' }> = {
          active: { label: '生效中', type: 'success' },
          pending: { label: '待签署', type: 'warning' },
          terminated: { label: '已终止', type: 'error' },
          expired: { label: '已过期', type: 'info' }
        };
        const status = statusMap[row.status] || { label: row.status || '-', type: 'info' };
        return <NTag type={status.type}>{status.label}</NTag>;
      }
    },
    {
      key: 'signTime',
      title: '签署时间',
      align: 'center',
      minWidth: 120,
      render: row => {
        if (!row.signTime) return '-';
        return new Date(row.signTime).toLocaleDateString();
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 130,
      render: row => {
        const divider = () => {
          if (!hasAuth('business:contract:edit') || !hasAuth('business:contract:remove')) {
            return null;
          }
          return <NDivider vertical />;
        };

        const editBtn = () => {
          if (!hasAuth('business:contract:edit')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="primary"
              icon="material-symbols:drive-file-rename-outline-outline"
              tooltipContent={$t('common.edit')}
              onClick={() => edit(row.id!)}
            />
          );
        };

        const deleteBtn = () => {
          if (!hasAuth('business:contract:remove')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="error"
              icon="material-symbols:delete-outline"
              tooltipContent={$t('common.delete')}
              popconfirmContent={$t('common.confirmDelete')}
              onPositiveClick={() => handleDelete(row.id!)}
            />
          );
        };

        return (
          <div class="flex-center gap-8px">
            {editBtn()}
            {divider()}
            {deleteBtn()}
          </div>
        );
      }
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  // request
  const { error } = await fetchBatchDeleteContract(checkedRowKeys.value);
  if (error) return;
  onBatchDeleted();
}

async function handleDelete(id: CommonType.IdType) {
  // request
  const { error } = await fetchBatchDeleteContract([id]);
  if (error) return;
  onDeleted();
}

function edit(id: CommonType.IdType) {
  handleEdit('id', id);
}

function handleExport() {
  download('/business/leaseContract/export', searchParams, `合同管理_${new Date().getTime()}.xlsx`);
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <ContractSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard title="合同管理" :bordered="false" size="small" class="card-wrapper sm:flex-1-hidden">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          :show-add="hasAuth('business:contract:add')"
          :show-delete="hasAuth('business:contract:remove')"
          :show-export="hasAuth('business:contract:export')"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @export="handleExport"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <ContractOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
