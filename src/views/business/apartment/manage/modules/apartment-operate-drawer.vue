<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { facilitiesOptions, houseTypeOptions, roomStatusOptions } from '@/constants/apartment';
import { fetchCreateApartment, fetchUpdateApartment } from '@/service/api/business/apartment';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import OssUpload from '@/components/custom/oss-upload.vue';

defineOptions({
  name: 'ApartmentOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Business.Apartment | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { createRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增公寓',
    edit: '编辑公寓'
  };
  return titles[props.operateType];
});

type Model = Api.Business.ApartmentOperateParams;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    buildingNumber: '',
    floorNumber: '',
    roomNumber: '',
    houseType: null,
    area: null,
    facilities: null,
    roomStatus: null,
    occupancyLimit: null,
    mainImageUrl: '',
    imageUrls: '',
    remark: ''
  };
}

type RuleKey = Extract<
  keyof Model,
  | 'buildingNumber'
  | 'floorNumber'
  | 'roomNumber'
  | 'houseType'
  | 'area'
  | 'facilities'
  | 'roomStatus'
  | 'occupancyLimit'
  | 'mainImageUrl'
  | 'imageUrls'
  | 'remark'
  | 'createTime'
  | 'createBy'
  | 'updateTime'
  | 'updateBy'
  | 'version'
>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  buildingNumber: createRequiredRule('楼栋号不能为空'),
  floorNumber: createRequiredRule('楼层号不能为空'),
  roomNumber: createRequiredRule('房间号不能为空'),
  houseType: createRequiredRule('户型不能为空'),
  area: [createRequiredRule('面积不能为空'), { type: 'number', min: 0.1, message: '面积必须大于0', trigger: 'blur' }],
  facilities: [],
  roomStatus: createRequiredRule('房间状态不能为空'),
  occupancyLimit: [{ type: 'number', min: 1, message: '入住人数必须大于0', trigger: 'blur' }],
  mainImageUrl: [],
  imageUrls: [],
  remark: [],
  createTime: [],
  createBy: [],
  updateTime: [],
  updateBy: [],
  version: []
};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    const editData = { ...props.rowData };
    // 将逗号分隔的设施字符串转换为数组
    if (editData.facilities && typeof editData.facilities === 'string') {
      editData.facilities = editData.facilities.split(',').filter(item => item.trim());
    }
    Object.assign(model, editData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  const {
    id,
    buildingNumber,
    floorNumber,
    roomNumber,
    houseType,
    area,
    facilities,
    roomStatus,
    occupancyLimit,
    mainImageUrl,
    imageUrls,
    remark
  } = model;

  // 将设施数组转换为逗号分隔的字符串
  const facilitiesStr = Array.isArray(facilities) ? facilities.join(',') : facilities || '';

  const submitData = {
    id,
    buildingNumber,
    floorNumber,
    roomNumber,
    houseType,
    area,
    facilities: facilitiesStr,
    roomStatus,
    occupancyLimit,
    mainImageUrl,
    imageUrls,
    remark
  };

  // request
  if (props.operateType === 'add') {
    const { error } = await fetchCreateApartment(submitData);
    if (error) return;
  }

  if (props.operateType === 'edit') {
    const { error } = await fetchUpdateApartment(submitData);
    if (error) return;
  }

  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="800" class="max-w-90%">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules" label-width="120">
        <NDivider title-placement="left">基本信息</NDivider>
        <NGrid :cols="2" :x-gap="16">
          <NGridItem>
            <NFormItem label="楼栋号" path="buildingNumber">
              <NInput v-model:value="model.buildingNumber" placeholder="请输入楼栋号" />
            </NFormItem>
          </NGridItem>
          <NGridItem>
            <NFormItem label="楼层号" path="floorNumber">
              <NInput v-model:value="model.floorNumber" placeholder="请输入楼层号" />
            </NFormItem>
          </NGridItem>
          <NGridItem>
            <NFormItem label="房间号" path="roomNumber">
              <NInput v-model:value="model.roomNumber" placeholder="请输入房间号" />
            </NFormItem>
          </NGridItem>
          <NGridItem>
            <NFormItem label="户型" path="houseType">
              <NSelect v-model:value="model.houseType" placeholder="请选择户型" :options="houseTypeOptions" clearable />
            </NFormItem>
          </NGridItem>
          <NGridItem>
            <NFormItem label="面积(㎡)" path="area">
              <NInputNumber
                v-model:value="model.area"
                placeholder="请输入面积"
                :min="0.1"
                :precision="1"
                style="width: 100%"
              >
                <template #suffix>㎡</template>
              </NInputNumber>
            </NFormItem>
          </NGridItem>
          <NGridItem>
            <NFormItem label="房间状态" path="roomStatus">
              <NSelect
                v-model:value="model.roomStatus"
                placeholder="请选择房间状态"
                :options="roomStatusOptions"
                clearable
              />
            </NFormItem>
          </NGridItem>
        </NGrid>

        <NDivider title-placement="left">入住信息</NDivider>
        <NFormItem label="入住人数" path="occupancyLimit">
          <NInputNumber
            v-model:value="model.occupancyLimit"
            placeholder="请输入入住人数"
            :min="1"
            :precision="0"
            style="width: 200px"
          >
            <template #suffix>人</template>
          </NInputNumber>
        </NFormItem>

        <NDivider title-placement="left">配套设施</NDivider>
        <NFormItem label="设施配置" path="facilities">
          <NSelect
            v-model:value="model.facilities"
            placeholder="请选择设施配置"
            :options="facilitiesOptions"
            multiple
            clearable
            max-tag-count="responsive"
          />
        </NFormItem>

        <NDivider title-placement="left">图片信息</NDivider>
        <NFormItem label="主图" path="mainImageUrl">
          <OssUpload v-model:value="model.mainImageUrl" upload-type="image" :max="1" :file-size="5" />
        </NFormItem>
        <NFormItem label="房间图片" path="imageUrls">
          <OssUpload v-model:value="model.imageUrls" upload-type="image" :max="10" :file-size="5" />
        </NFormItem>

        <NDivider title-placement="left">其他信息</NDivider>
        <NFormItem label="备注" path="remark">
          <NInput v-model:value="model.remark" :rows="3" type="textarea" placeholder="请输入备注信息" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
