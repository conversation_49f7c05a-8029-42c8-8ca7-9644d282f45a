/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
    /**
     * namespace Business
     *
     * backend api module: "Business"
     */
    namespace Business {
        /** payment */
        type Payment = Common.CommonRecord<{
            /** 主键，唯一标识每条提醒记录 */
                id: CommonType.IdType; 
            /** 提醒编号（如REM20240415001） */
                reminderNo: string; 
            /** 关联租赁合同表的合同 ID */
                contractId: CommonType.IdType; 
            /** 提醒类型（缴费提醒、续签提醒） */
                reminderType: string; 
            /** 计划提醒时间（精确到分秒） */
                reminderTime: string; 
            /** 提前提醒天数 */
                advanceDays: number; 
            /** 发送方式（短信、微信、邮件等，多方式用逗号分隔） */
                sendMethod: string; 
            /** 提醒内容模板 */
                messageContent: string; 
            /** 提醒状态（未发送、已发送、发送失败、已取消） */
                status: string; 
            /** 实际发送时间（精确到分秒） */
                sendTime: string; 
            /** 发送结果描述（如失败原因） */
                sendResult: string; 
            /** 关于提醒的备注信息 */
                remark: string; 
            /** 版本号，用于乐观锁控制 */
                version: number; 
        }>;

        /** payment search params */
        type PaymentSearchParams = CommonType.RecordNullable<
            Pick<
                Api.Business.Payment,
                        | 'reminderNo'
                        | 'contractId'
                        | 'reminderType'
                        | 'reminderTime'
                        | 'advanceDays'
                        | 'sendMethod'
                        | 'messageContent'
                        | 'status'
                        | 'sendTime'
                        | 'sendResult'
            > &
            Api.Common.CommonSearchParams
        >;

        /** payment operate params */
        type PaymentOperateParams = CommonType.RecordNullable<
            Pick<
                Api.Business.Payment,
                        | 'id'
                        | 'reminderNo'
                        | 'contractId'
                        | 'reminderType'
                        | 'reminderTime'
                        | 'advanceDays'
                        | 'sendMethod'
                        | 'messageContent'
                        | 'status'
                        | 'sendTime'
                        | 'sendResult'
                        | 'remark'
            >
        >;

        /** payment list */
        type PaymentList = Api.Common.PaginatingQueryRecord<Payment>;
    }
}
