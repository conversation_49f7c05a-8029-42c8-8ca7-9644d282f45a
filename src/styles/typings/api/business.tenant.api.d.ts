/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
    /**
     * namespace Business
     *
     * backend api module: "Business"
     */
    namespace Business {
        /** tenant */
        type Tenant = Common.CommonRecord<{
            /** 主键，唯一标识每个租户 */
                id: CommonType.IdType; 
            /** 租户编号，系统自动生成，如TEN20231015001 */
                tenantNo: string; 
            /** 租户姓名 */
                name: string; 
            /** 性别 */
                gender: string; 
            /** 身份证号码 */
                idCardNumber: CommonType.IdType; 
            /** 身份证正面照片URL */
                idCardFrontUrl: CommonType.IdType; 
            /** 身份证反面照片URL */
                idCardBackUrl: CommonType.IdType; 
            /** 出生日期 */
                dateOfBirth: string; 
            /** 民族 */
                nation: string; 
            /** 联系方式 */
                contactNumber: string; 
            /** 紧急联系人姓名 */
                emergencyContact: string; 
            /** 紧急联系人电话 */
                emergencyContactNumber: string; 
            /** 公司信息 */
                companyInfo: string; 
            /** 户籍地址 */
                householdRegister: string; 
            /** 现住址 */
                currentAddress: string; 
            /** 入住时间 */
                checkInTime: string; 
            /** 退租时间 */
                checkOutTime: string; 
            /** 租约类型：长期/短期 */
                leaseTermType: string; 
            /** 预计租期(月)，辅助判断租约类型 */
                expectedLeaseTerm: number; 
            /** 租户状态 */
                status: string; 
            /** 头像存储路径 */
                avatarUrl: string; 
            /** 关于租户的备注信息 */
                remark: string; 
            /** 版本号，用于乐观锁控制 */
                version: number; 
        }>;

        /** tenant search params */
        type TenantSearchParams = CommonType.RecordNullable<
            Pick<
                Api.Business.Tenant,
                        | 'tenantNo'
                        | 'name'
                        | 'gender'
                        | 'idCardNumber'
                        | 'idCardFrontUrl'
                        | 'idCardBackUrl'
                        | 'dateOfBirth'
                        | 'nation'
                        | 'contactNumber'
                        | 'emergencyContact'
                        | 'emergencyContactNumber'
                        | 'companyInfo'
                        | 'householdRegister'
                        | 'currentAddress'
                        | 'checkInTime'
                        | 'checkOutTime'
                        | 'leaseTermType'
                        | 'expectedLeaseTerm'
                        | 'status'
                        | 'avatarUrl'
            > &
            Api.Common.CommonSearchParams
        >;

        /** tenant operate params */
        type TenantOperateParams = CommonType.RecordNullable<
            Pick<
                Api.Business.Tenant,
                        | 'id'
                        | 'tenantNo'
                        | 'name'
                        | 'gender'
                        | 'idCardNumber'
                        | 'idCardFrontUrl'
                        | 'idCardBackUrl'
                        | 'dateOfBirth'
                        | 'nation'
                        | 'contactNumber'
                        | 'emergencyContact'
                        | 'emergencyContactNumber'
                        | 'companyInfo'
                        | 'householdRegister'
                        | 'currentAddress'
                        | 'checkInTime'
                        | 'checkOutTime'
                        | 'leaseTermType'
                        | 'expectedLeaseTerm'
                        | 'status'
                        | 'avatarUrl'
                        | 'remark'
            >
        >;

        /** tenant list */
        type TenantList = Api.Common.PaginatingQueryRecord<Tenant>;
    }
}
