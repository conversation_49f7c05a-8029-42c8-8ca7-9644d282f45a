/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  /**
   * namespace Business
   *
   * backend api module: "Business"
   */
  namespace Business {
    /** contract */
    type Contract = Common.CommonRecord<{
      /** 主键ID */
      id: CommonType.IdType;
      /** 合同编号（如CON20240401001） */
      contractNo: string;
      /** 租户ID，关联tenant_info表 */
      tenantId: CommonType.IdType;
      /** 公寓ID，关联apartment_info表 */
      apartmentId: CommonType.IdType;
      /** 甲方单位名称 */
      partyAName: string;
      /** 甲方统一社会信用代码 */
      partyACreditCode: string;
      /** 甲方代表人 */
      partyARepresentative: string;
      /** 甲方联系电话 */
      partyAContact: string;
      /** 甲方地址 */
      partyAAddress: string;
      /** 乙方姓名 */
      partyBName: string;
      /** 乙方身份证号 */
      partyBIdCard: CommonType.IdType;
      /** 乙方联系电话 */
      partyBContact: string;
      /** 乙方紧急联系人 */
      partyBEmergencyContact: string;
      /** 乙方紧急联系电话 */
      partyBEmergencyPhone: string;
      /** 租赁起始时间 */
      leaseStartTime: string;
      /** 租赁结束时间 */
      leaseEndTime: string;
      /** 租赁总天数（计算列） */
      leaseTermDays: number;
      /** 最大居住人数 */
      maxOccupancy: number;
      /** 月租金 */
      monthlyRent: number;
      /** 押金金额 */
      depositAmount: number;
      /** 押金类型（如：2个月租金） */
      depositType: string;
      /** 支付周期 */
      paymentCycle: string;
      /** 每月付款日（如15号） */
      paymentDate: number;
      /** 物业费 */
      propertyFee: number;
      /** 费用是否包含水电费 */
      feeIncludeWaterElectric: number;
      /** 合同状态 */
      status: string;
      /** 签署时间 */
      signTime: string;
      /** 合同文件URL */
      contractFileUrl: string;
      /** 签字页扫描件URL */
      signFileUrl: string;
      /** 备注信息 */
      remark: string;
      /** 版本号（乐观锁） */
      version: number;
      /** 终止时间 */
      terminationTime: string;
      /** 终止原因 */
      terminationReason: string;
    }>;

    /** contract search params */
    type ContractSearchParams = CommonType.RecordNullable<
      Pick<
        Api.Business.Contract,
        | 'contractNo'
        | 'apartmentId'
        | 'partyAName'
        | 'partyACreditCode'
        | 'partyARepresentative'
        | 'partyAContact'
        | 'partyAAddress'
        | 'partyBName'
        | 'partyBIdCard'
        | 'partyBContact'
        | 'partyBEmergencyContact'
        | 'partyBEmergencyPhone'
        | 'leaseStartTime'
        | 'leaseEndTime'
        | 'leaseTermDays'
        | 'maxOccupancy'
        | 'monthlyRent'
        | 'depositAmount'
        | 'depositType'
        | 'paymentCycle'
        | 'paymentDate'
        | 'propertyFee'
        | 'feeIncludeWaterElectric'
        | 'status'
        | 'signTime'
        | 'contractFileUrl'
        | 'signFileUrl'
        | 'terminationTime'
        | 'terminationReason'
      > &
        Api.Common.CommonSearchParams
    >;

    /** contract operate params */
    type ContractOperateParams = CommonType.RecordNullable<
      Pick<
        Api.Business.Contract,
        | 'id'
        | 'contractNo'
        | 'apartmentId'
        | 'partyAName'
        | 'partyACreditCode'
        | 'partyARepresentative'
        | 'partyAContact'
        | 'partyAAddress'
        | 'partyBName'
        | 'partyBIdCard'
        | 'partyBContact'
        | 'partyBEmergencyContact'
        | 'partyBEmergencyPhone'
        | 'leaseStartTime'
        | 'leaseEndTime'
        | 'leaseTermDays'
        | 'maxOccupancy'
        | 'monthlyRent'
        | 'depositAmount'
        | 'depositType'
        | 'paymentCycle'
        | 'paymentDate'
        | 'propertyFee'
        | 'feeIncludeWaterElectric'
        | 'status'
        | 'signTime'
        | 'contractFileUrl'
        | 'signFileUrl'
        | 'remark'
        | 'terminationTime'
        | 'terminationReason'
      >
    >;

    /** contract list */
    type ContractList = Api.Common.PaginatingQueryRecord<Contract>;
  }
}
