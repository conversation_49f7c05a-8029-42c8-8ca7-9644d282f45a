import { request } from '@/service/request';

/** 获取缴费计划管理列表 */
export function fetchGetPaymentList(params?: Api.Business.PaymentSearchParams) {
  return request<Api.Business.PaymentList>({
    url: '/business/paymentReminder/list',
    method: 'get',
    params
  });
}

/** 新增缴费计划管理 */
export function fetchCreatePayment(data: Api.Business.PaymentOperateParams) {
  return request<boolean>({
    url: '/business/paymentReminder',
    method: 'post',
    data
  });
}

/** 修改缴费计划管理 */
export function fetchUpdatePayment(data: Api.Business.PaymentOperateParams) {
  return request<boolean>({
    url: '/business/paymentReminder',
    method: 'put',
    data
  });
}

/** 批量删除缴费计划管理 */
export function fetchBatchDeletePayment(ids: CommonType.IdType[]) {
  return request<boolean>({
    url: `/business/paymentReminder/${ids.join(',')}`,
    method: 'delete'
  });
}
