import { request } from '@/service/request';

/** 获取租户信息管理列表 */
export function fetchGetTenantList(params?: Api.Business.TenantSearchParams) {
  return request<Api.Business.TenantList>({
    url: '/business/tenantInfo/list',
    method: 'get',
    params
  });
}

/** 新增租户信息管理 */
export function fetchCreateTenant(data: Api.Business.TenantOperateParams) {
  return request<boolean>({
    url: '/business/tenantInfo',
    method: 'post',
    data
  });
}

/** 修改租户信息管理 */
export function fetchUpdateTenant(data: Api.Business.TenantOperateParams) {
  return request<boolean>({
    url: '/business/tenantInfo',
    method: 'put',
    data
  });
}

/** 批量删除租户信息管理 */
export function fetchBatchDeleteTenant(ids: CommonType.IdType[]) {
  return request<boolean>({
    url: `/business/tenantInfo/${ids.join(',')}`,
    method: 'delete'
  });
}
