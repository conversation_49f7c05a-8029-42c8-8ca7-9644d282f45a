import { request } from '@/service/request';

/** 获取租赁合同管理列表 */
export function fetchGetContractList(params?: Api.Business.ContractSearchParams) {
  return request<Api.Business.ContractList>({
    url: '/business/leaseContract/list',
    method: 'get',
    params
  });
}

/** 新增租赁合同管理 */
export function fetchCreateContract(data: Api.Business.ContractOperateParams) {
  return request<boolean>({
    url: '/business/leaseContract',
    method: 'post',
    data
  });
}

/** 修改租赁合同管理 */
export function fetchUpdateContract(data: Api.Business.ContractOperateParams) {
  return request<boolean>({
    url: '/business/leaseContract',
    method: 'put',
    data
  });
}

/** 批量删除租赁合同管理 */
export function fetchBatchDeleteContract(ids: CommonType.IdType[]) {
  return request<boolean>({
    url: `/business/leaseContract/${ids.join(',')}`,
    method: 'delete'
  });
}
