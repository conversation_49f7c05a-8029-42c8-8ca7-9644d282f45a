import { transformRecordToOption } from '@/utils/common';

/** 户型选项 */
export const houseTypeRecord: Record<string, string> = {
  '1': '一居室',
  '2': '两居室',
  '3': '三居室',
  '4': '四居室',
  '5': '五居室',
  'studio': '开间',
  'loft': 'LOFT',
  'duplex': '复式'
};

export const houseTypeOptions = transformRecordToOption(houseTypeRecord);

/** 房间状态选项 */
export const roomStatusRecord: Record<string, string> = {
  'available': '空闲',
  'occupied': '已入住',
  'maintenance': '维修中',
  'reserved': '已预定',
  'cleaning': '清洁中',
  'disabled': '停用'
};

export const roomStatusOptions = transformRecordToOption(roomStatusRecord);

/** 设施选项 */
export const facilitiesRecord: Record<string, string> = {
  'air_conditioner': '空调',
  'washing_machine': '洗衣机',
  'refrigerator': '冰箱',
  'water_heater': '热水器',
  'wifi': 'WiFi',
  'tv': '电视',
  'microwave': '微波炉',
  'desk': '书桌',
  'wardrobe': '衣柜',
  'bed': '床',
  'sofa': '沙发',
  'balcony': '阳台',
  'parking': '停车位'
};

export const facilitiesOptions = transformRecordToOption(facilitiesRecord);

/** 房间状态标签颜色映射 */
export const roomStatusTagColors: Record<string, string> = {
  'available': 'success',
  'occupied': 'warning',
  'maintenance': 'error',
  'reserved': 'info',
  'cleaning': 'default',
  'disabled': 'error'
};
